using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using System.Security.Claims;
using WTO.IdbSubmissions.Application.Common.Interfaces;
using WTO.IdbSubmissions.Domain.Entities;
using WTO.IdbSubmissions.Persistence.Data;
using WTO.IdbSubmissions.Persistence.Interceptors;
using WTO.IdbSubmissions.Persistence.Services;

namespace WTO.IdbSubmissions.IntegrationTests.Persistence;

/// <summary>
/// Integration tests for the AuditInterceptor to verify automatic audit field population
/// </summary>
[Trait("Category", "Integration")]
[Trait("Layer", "Persistence")]
public class AuditInterceptorTests : IDisposable
{
    private readonly IdbSubmissionsDbContext _context;
    private readonly ICurrentUserService _currentUserService;
    private readonly IServiceProvider _serviceProvider;

    public AuditInterceptorTests()
    {
        // Setup service collection
        var services = new ServiceCollection();
        
        // Add HTTP context accessor with a mock user
        services.AddSingleton<IHttpContextAccessor>(provider =>
        {
            var httpContextAccessor = new HttpContextAccessor();
            var httpContext = new DefaultHttpContext();
            
            // Setup a mock authenticated user
            var claims = new[]
            {
                new Claim(ClaimTypes.NameIdentifier, "test-user-123"),
                new Claim(ClaimTypes.Name, "Test User"),
                new Claim("preferred_username", "<EMAIL>")
            };
            
            var identity = new ClaimsIdentity(claims, "Test");
            var principal = new ClaimsPrincipal(identity);
            httpContext.User = principal;
            
            httpContextAccessor.HttpContext = httpContext;
            return httpContextAccessor;
        });
        
        // Add current user service
        services.AddScoped<ICurrentUserService, CurrentUserService>();
        
        // Add audit interceptor
        services.AddScoped<AuditInterceptor>();
        
        // Add in-memory database with interceptor
        services.AddDbContext<IdbSubmissionsDbContext>((serviceProvider, options) =>
        {
            var auditInterceptor = serviceProvider.GetRequiredService<AuditInterceptor>();
            options.UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}")
                .AddInterceptors(auditInterceptor);
        });
        
        _serviceProvider = services.BuildServiceProvider();
        _context = _serviceProvider.GetRequiredService<IdbSubmissionsDbContext>();
        _currentUserService = _serviceProvider.GetRequiredService<ICurrentUserService>();
    }

    [Fact]
    public async Task SaveChangesAsync_WhenAddingEntity_ShouldPopulateCreatedByAndCreatedAt()
    {
        // Arrange
        var user = new User("John", "Doe", "<EMAIL>", 30);
        
        // Act
        _context.Users.Add(user);
        await _context.SaveChangesAsync();
        
        // Assert
        user.CreatedBy.Should().Be("test-user-123");
        user.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(1));
        user.ModifiedBy.Should().BeNull();
        user.ModifiedAt.Should().BeNull();
    }

    [Fact]
    public async Task SaveChangesAsync_WhenModifyingEntity_ShouldPopulateModifiedByAndModifiedAt()
    {
        // Arrange
        var user = new User("John", "Doe", "<EMAIL>", 30);
        _context.Users.Add(user);
        await _context.SaveChangesAsync();
        
        var originalCreatedBy = user.CreatedBy;
        var originalCreatedAt = user.CreatedAt;
        
        // Wait a small amount to ensure different timestamps
        await Task.Delay(10);
        
        // Act
        user.UpdateEmail("<EMAIL>");
        await _context.SaveChangesAsync();
        
        // Assert
        user.CreatedBy.Should().Be(originalCreatedBy); // CreatedBy should not change
        user.CreatedAt.Should().Be(originalCreatedAt); // CreatedAt should not change
        user.ModifiedBy.Should().Be("test-user-123");
        user.ModifiedAt.Should().NotBeNull();
        user.ModifiedAt.Should().BeAfter(originalCreatedAt);
    }

    [Fact]
    public async Task SaveChangesAsync_WhenDeletingEntity_ShouldPopulateDeletedByAndDeletedAt()
    {
        // Arrange
        var user = new User("John", "Doe", "<EMAIL>", 30);
        _context.Users.Add(user);
        await _context.SaveChangesAsync();
        
        // Act
        _context.Users.Remove(user);
        await _context.SaveChangesAsync();
        
        // Assert - Entity should be soft deleted
        user.IsDeleted.Should().BeTrue();
        user.DeletedBy.Should().Be("test-user-123");
        user.DeletedAt.Should().NotBeNull();
        user.DeletedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(1));
        
        // Verify entity is not physically deleted but marked as deleted
        var entityEntry = _context.Entry(user);
        entityEntry.State.Should().Be(EntityState.Unchanged);
    }

    [Fact]
    public async Task SaveChanges_SynchronousVersion_ShouldAlsoPopulateAuditFields()
    {
        // Arrange
        var user = new User("Jane", "Smith", "<EMAIL>", 25);
        
        // Act
        _context.Users.Add(user);
        _context.SaveChanges(); // Using synchronous version
        
        // Assert
        user.CreatedBy.Should().Be("test-user-123");
        user.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(1));
    }

    [Fact]
    public void CurrentUserService_ShouldReturnCorrectUserId()
    {
        // Act
        var userId = _currentUserService.GetCurrentUserId();
        var userName = _currentUserService.GetCurrentUserName();
        var isAuthenticated = _currentUserService.IsAuthenticated();
        
        // Assert
        userId.Should().Be("test-user-123");
        userName.Should().Be("Test User");
        isAuthenticated.Should().BeTrue();
    }

    public void Dispose()
    {
        _context?.Dispose();
        _serviceProvider?.GetService<IServiceScope>()?.Dispose();
    }
}
