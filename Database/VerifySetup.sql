-- =============================================
-- Verify Database Setup
-- =============================================

USE [IdbSubmissions]
GO

PRINT '=== Database Setup Verification ==='
PRINT ''

-- Check if database exists
IF DB_NAME() = 'IdbSubmissions'
BEGIN
    PRINT '✓ Connected to IdbSubmissions database'
END
ELSE
BEGIN
    PRINT '✗ Not connected to IdbSubmissions database'
    RETURN
END

-- Check if Users table exists
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND type in (N'U'))
BEGIN
    PRINT '✓ Users table exists'
    
    -- Check Users table structure
    SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE,
        CHARACTER_MAXIMUM_LENGTH,
        COLUMN_DEFAULT
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'Users'
    ORDER BY ORDINAL_POSITION
END
ELSE
BEGIN
    PRINT '✗ Users table does not exist'
END

PRINT ''

-- Check if TariffSubmissions table exists
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[TariffSubmissions]') AND type in (N'U'))
BEGIN
    PRINT '✓ TariffSubmissions table exists'
    
    -- Check TariffSubmissions table structure
    SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE,
        CHARACTER_MAXIMUM_LENGTH,
        COLUMN_DEFAULT
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'TariffSubmissions'
    ORDER BY ORDINAL_POSITION
END
ELSE
BEGIN
    PRINT '✗ TariffSubmissions table does not exist'
END

PRINT ''

-- Check indexes
PRINT '=== Index Verification ==='

-- Check Users email index
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND name = N'IX_Users_Email')
BEGIN
    PRINT '✓ IX_Users_Email index exists'
END
ELSE
BEGIN
    PRINT '✗ IX_Users_Email index does not exist'
END

-- Check TariffSubmissions composite index
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[TariffSubmissions]') AND name = N'IX_TariffSubmissions_WTOCountryCode_Year')
BEGIN
    PRINT '✓ IX_TariffSubmissions_WTOCountryCode_Year index exists'
END
ELSE
BEGIN
    PRINT '✗ IX_TariffSubmissions_WTOCountryCode_Year index does not exist'
END

PRINT ''
PRINT '=== Verification Complete ==='

-- Show table row counts
SELECT 
    'Users' as TableName,
    COUNT(*) as RowCount
FROM Users
UNION ALL
SELECT 
    'TariffSubmissions' as TableName,
    COUNT(*) as RowCount
FROM TariffSubmissions
