-- =============================================
-- Create IdbSubmissions Database
-- =============================================

-- Check if database exists, create if it doesn't
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = N'IdbSubmissions')
BEGIN
    CREATE DATABASE [IdbSubmissions]
    PRINT 'IdbSubmissions database created successfully.'
END
ELSE
BEGIN
    PRINT 'IdbSubmissions database already exists.'
END
GO

-- Switch to the database
USE [IdbSubmissions]
GO

PRINT 'Database creation script completed successfully.'
