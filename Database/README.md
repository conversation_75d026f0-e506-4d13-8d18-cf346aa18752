# Database Setup - Manual Table Creation

This project uses Entity Framework Core but **does not use migrations**. Instead, database tables are created manually using SQL scripts.

## Prerequisites

- SQL Server (LocalDB, Express, or full version)
- SQL Server Management Studio (SSMS) or Azure Data Studio (recommended)

## Database Setup Steps

### 1. Create the Database

Run the following script to create the `IdbSubmissions` database:

```sql
-- Execute: Database/CreateDatabase.sql
```

Or manually execute:
```sql
CREATE DATABASE [IdbSubmissions]
```

### 2. Create Tables and Indexes

Run the following script to create all tables and indexes:

```sql
-- Execute: Database/CreateTables.sql
```

This script will create:
- **Users** table with all required columns and indexes
- **TariffSubmissions** table with all required columns and indexes
- Unique indexes for data integrity
- Proper constraints and data types

### 3. Verify Connection String

Ensure your `appsettings.json` has the correct connection string:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=IdbSubmissions;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true"
  }
}
```

## Table Structure

### Users Table
- **Id** (uniqueidentifier, PK) - Primary key
- **FirstName** (nvarchar(100), NOT NULL) - User's first name
- **LastName** (nvarchar(100), NOT NULL) - User's last name  
- **Email** (nvarchar(255), NOT NULL, UNIQUE) - User's email address
- **Age** (int, NOT NULL) - User's age
- **IsActive** (bit, NOT NULL) - Whether user is active
- **CreatedAt** (datetime2, NOT NULL) - Creation timestamp
- **ModifiedAt** (datetime2, NULL) - Last modification timestamp
- **CreatedBy** (nvarchar(255), NULL) - User who created the record
- **ModifiedBy** (nvarchar(255), NULL) - User who last modified the record
- **IsDeleted** (bit, NOT NULL, DEFAULT 0) - Soft delete flag
- **DeletedAt** (datetime2, NULL) - Deletion timestamp
- **DeletedBy** (nvarchar(255), NULL) - User who deleted the record

### TariffSubmissions Table
- **Id** (uniqueidentifier, PK) - Primary key
- **WTOCountryCode** (nvarchar(3), NOT NULL) - ISO country code
- **Year** (int, NOT NULL) - Submission year
- **Status** (nvarchar(max), NOT NULL) - Submission status (enum as string)
- **SubmittedAt** (datetime2, NULL) - Submission timestamp
- **SubmittedBy** (nvarchar(255), NULL) - User who submitted
- **AdditionalContactEmails** (nvarchar(1000), NULL) - Additional contact emails
- **TariffSubmissionOrigin** (nvarchar(max), NOT NULL) - Origin (enum as string)
- **OriginalLanguage** (nvarchar(max), NOT NULL) - Language (enum as string)
- **Currency** (nvarchar(3), NOT NULL) - ISO currency code
- **PreferentialTariffs** (bit, NOT NULL) - Whether preferential tariffs included
- **BeneficiaryList** (bit, NOT NULL) - Whether beneficiary list included
- **OtherDutiesAndCharges** (bit, NOT NULL) - Whether other duties included
- **DutiesApplicableFrom** (date, NOT NULL) - Start date for duties
- **DutiesApplicableTo** (date, NOT NULL) - End date for duties
- **Description** (nvarchar(2000), NULL) - Additional description
- **CreatedAt** (datetime2, NOT NULL) - Creation timestamp
- **ModifiedAt** (datetime2, NULL) - Last modification timestamp
- **CreatedBy** (nvarchar(255), NULL) - User who created the record
- **ModifiedBy** (nvarchar(255), NULL) - User who last modified the record
- **IsDeleted** (bit, NOT NULL, DEFAULT 0) - Soft delete flag
- **DeletedAt** (datetime2, NULL) - Deletion timestamp
- **DeletedBy** (nvarchar(255), NULL) - User who deleted the record

## Indexes

- **IX_Users_Email** - Unique index on Users.Email
- **IX_TariffSubmissions_WTOCountryCode_Year** - Unique composite index on (WTOCountryCode, Year) with filter for non-deleted records

## Important Notes

1. **No Migrations**: This project does not use EF Core migrations. All schema changes must be done manually.

2. **Soft Deletes**: Both tables implement soft delete patterns. Records are never physically deleted, only marked as deleted.

3. **Audit Fields**: All tables include audit fields (CreatedAt, CreatedBy, ModifiedAt, ModifiedBy, etc.) that are automatically populated by the application.

4. **GUID Primary Keys**: All entities use GUID primary keys that are generated in the application domain layer.

5. **Enum Storage**: Enums are stored as strings in the database for better readability and maintainability.

## Testing

After running the scripts, you can test the setup by:

1. Starting the application
2. The application should connect successfully without any migration errors
3. Check the logs for any database connection issues

## Troubleshooting

- **Connection Issues**: Verify SQL Server is running and the connection string is correct
- **Permission Issues**: Ensure the application has appropriate database permissions
- **Table Already Exists**: The scripts include checks to prevent errors if tables already exist
