using WTO.IdbSubmissions.Domain.Events;

namespace WTO.IdbSubmissions.Domain.Common;

/// <summary>
/// Base entity class that provides common properties for all entities
/// </summary>
public abstract class BaseEntity
{
    private readonly List<IDomainEvent> _domainEvents = new();
    /// <summary>
    /// Unique identifier for the entity
    /// </summary>
    public Guid Id { get; protected set; } = Guid.NewGuid();

    /// <summary>
    /// Date and time when the entity was created
    /// Automatically populated by the audit interceptor
    /// </summary>
    public DateTime CreatedAt { get; internal set; } = DateTime.UtcNow;

    /// <summary>
    /// Date and time when the entity was last modified
    /// Automatically populated by the audit interceptor
    /// </summary>
    public DateTime? ModifiedAt { get; internal set; }

    /// <summary>
    /// User who created the entity
    /// Automatically populated by the audit interceptor
    /// </summary>
    public string? CreatedBy { get; internal set; }

    /// <summary>
    /// User who last modified the entity
    /// Automatically populated by the audit interceptor
    /// </summary>
    public string? ModifiedBy { get; internal set; }

    /// <summary>
    /// Indicates if the entity is soft deleted
    /// Automatically populated by the audit interceptor
    /// </summary>
    public bool IsDeleted { get; internal set; }

    /// <summary>
    /// Date and time when the entity was deleted
    /// Automatically populated by the audit interceptor
    /// </summary>
    public DateTime? DeletedAt { get; internal set; }

    /// <summary>
    /// User who deleted the entity
    /// Automatically populated by the audit interceptor
    /// </summary>
    public string? DeletedBy { get; internal set; }

    /// <summary>
    /// Updates the modification timestamp and user
    /// </summary>
    /// <param name="modifiedBy">User making the modification</param>
    public virtual void SetModified(string? modifiedBy = null)
    {
        ModifiedAt = DateTime.UtcNow;
        ModifiedBy = modifiedBy;
    }

    /// <summary>
    /// Soft deletes the entity
    /// </summary>
    /// <param name="deletedBy">User performing the deletion</param>
    public virtual void SetDeleted(string? deletedBy = null)
    {
        IsDeleted = true;
        DeletedAt = DateTime.UtcNow;
        DeletedBy = deletedBy;
    }

    /// <summary>
    /// Restores a soft deleted entity
    /// </summary>
    /// <param name="restoredBy">User performing the restoration</param>
    public virtual void SetRestored(string? restoredBy = null)
    {
        IsDeleted = false;
        DeletedAt = null;
        DeletedBy = null;
        SetModified(restoredBy);
    }

    /// <summary>
    /// Sets the created by user (typically used during entity creation)
    /// </summary>
    /// <param name="createdBy">User creating the entity</param>
    public virtual void SetCreatedBy(string? createdBy)
    {
        CreatedBy = createdBy;
    }

    /// <summary>
    /// Gets the domain events for this entity
    /// </summary>
    public IReadOnlyCollection<IDomainEvent> DomainEvents => _domainEvents.AsReadOnly();

    /// <summary>
    /// Adds a domain event to the entity
    /// </summary>
    /// <param name="domainEvent">Domain event to add</param>
    protected void AddDomainEvent(IDomainEvent domainEvent)
    {
        _domainEvents.Add(domainEvent);
    }

    /// <summary>
    /// Removes a domain event from the entity
    /// </summary>
    /// <param name="domainEvent">Domain event to remove</param>
    protected void RemoveDomainEvent(IDomainEvent domainEvent)
    {
        _domainEvents.Remove(domainEvent);
    }

    /// <summary>
    /// Clears all domain events from the entity
    /// </summary>
    public void ClearDomainEvents()
    {
        _domainEvents.Clear();
    }

    public override bool Equals(object? obj)
    {
        if (obj is not BaseEntity other)
            return false;

        if (ReferenceEquals(this, other))
            return true;

        if (GetType() != other.GetType())
            return false;

        return Id == other.Id;
    }

    public override int GetHashCode()
    {
        return Id.GetHashCode();
    }

    public static bool operator ==(BaseEntity? left, BaseEntity? right)
    {
        return Equals(left, right);
    }

    public static bool operator !=(BaseEntity? left, BaseEntity? right)
    {
        return !Equals(left, right);
    }
}
