namespace WTO.IdbSubmissions.Application.Common.Interfaces;

/// <summary>
/// Service for accessing the current authenticated user's information
/// </summary>
public interface ICurrentUserService
{
    /// <summary>
    /// Gets the current user's identifier
    /// </summary>
    /// <returns>Current user identifier or null if not authenticated</returns>
    string? GetCurrentUserId();

    /// <summary>
    /// Gets the current user's name
    /// </summary>
    /// <returns>Current user name or null if not authenticated</returns>
    string? GetCurrentUserName();

    /// <summary>
    /// Indicates whether a user is currently authenticated
    /// </summary>
    /// <returns>True if authenticated, false otherwise</returns>
    bool IsAuthenticated();
}
