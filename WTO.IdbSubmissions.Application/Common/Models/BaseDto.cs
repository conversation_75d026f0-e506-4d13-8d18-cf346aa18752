namespace WTO.IdbSubmissions.Application.Common.Models;

/// <summary>
/// Base class for Data Transfer Objects (DTOs)
/// Note: Audit fields (CreatedBy, ModifiedBy, DeletedBy) are automatically managed
/// by the system and should not be exposed in DTOs for security reasons
/// </summary>
public abstract class BaseDto
{
    /// <summary>
    /// Unique identifier
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Date and time when the entity was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Date and time when the entity was last modified
    /// </summary>
    public DateTime? ModifiedAt { get; set; }
}

/// <summary>
/// Base class for DTOs that support soft deletion
/// </summary>
public abstract class BaseSoftDeleteDto : BaseDto
{
    /// <summary>
    /// Indicates if the entity is soft deleted
    /// </summary>
    public bool IsDeleted { get; set; }

    /// <summary>
    /// Date and time when the entity was deleted
    /// </summary>
    public DateTime? DeletedAt { get; set; }

    /// <summary>
    /// User who deleted the entity
    /// </summary>
    public string? DeletedBy { get; set; }
}

/// <summary>
/// Generic result wrapper for operations
/// </summary>
/// <typeparam name="T">Type of the result data</typeparam>
public class Result<T>
{
    /// <summary>
    /// Indicates if the operation was successful
    /// </summary>
    public bool IsSuccess { get; private set; }

    /// <summary>
    /// The result data (only available if successful)
    /// </summary>
    public T? Data { get; private set; }

    /// <summary>
    /// Error message (only available if not successful)
    /// </summary>
    public string? ErrorMessage { get; private set; }

    /// <summary>
    /// Collection of validation errors
    /// </summary>
    public IEnumerable<string> Errors { get; private set; } = new List<string>();

    private Result(bool isSuccess, T? data, string? errorMessage, IEnumerable<string>? errors = null)
    {
        IsSuccess = isSuccess;
        Data = data;
        ErrorMessage = errorMessage;
        Errors = errors ?? new List<string>();
    }

    /// <summary>
    /// Creates a successful result
    /// </summary>
    /// <param name="data">The result data</param>
    /// <returns>Successful result</returns>
    public static Result<T> Success(T data)
    {
        return new Result<T>(true, data, null);
    }

    /// <summary>
    /// Creates a failed result with an error message
    /// </summary>
    /// <param name="errorMessage">The error message</param>
    /// <returns>Failed result</returns>
    public static Result<T> Failure(string errorMessage)
    {
        return new Result<T>(false, default, errorMessage);
    }

    /// <summary>
    /// Creates a failed result with multiple errors
    /// </summary>
    /// <param name="errors">Collection of error messages</param>
    /// <returns>Failed result</returns>
    public static Result<T> Failure(IEnumerable<string> errors)
    {
        return new Result<T>(false, default, "Multiple errors occurred", errors);
    }
}

/// <summary>
/// Non-generic result wrapper for operations that don't return data
/// </summary>
public class Result
{
    /// <summary>
    /// Indicates if the operation was successful
    /// </summary>
    public bool IsSuccess { get; private set; }

    /// <summary>
    /// Error message (only available if not successful)
    /// </summary>
    public string? ErrorMessage { get; private set; }

    /// <summary>
    /// Collection of validation errors
    /// </summary>
    public IEnumerable<string> Errors { get; private set; } = new List<string>();

    private Result(bool isSuccess, string? errorMessage, IEnumerable<string>? errors = null)
    {
        IsSuccess = isSuccess;
        ErrorMessage = errorMessage;
        Errors = errors ?? new List<string>();
    }

    /// <summary>
    /// Creates a successful result
    /// </summary>
    /// <returns>Successful result</returns>
    public static Result Success()
    {
        return new Result(true, null);
    }

    /// <summary>
    /// Creates a failed result with an error message
    /// </summary>
    /// <param name="errorMessage">The error message</param>
    /// <returns>Failed result</returns>
    public static Result Failure(string errorMessage)
    {
        return new Result(false, errorMessage);
    }

    /// <summary>
    /// Creates a failed result with multiple errors
    /// </summary>
    /// <param name="errors">Collection of error messages</param>
    /// <returns>Failed result</returns>
    public static Result Failure(IEnumerable<string> errors)
    {
        return new Result(false, "Multiple errors occurred", errors);
    }
}
