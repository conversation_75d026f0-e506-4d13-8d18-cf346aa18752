using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using WTO.IdbSubmissions.Application.Common.Interfaces;
using WTO.IdbSubmissions.Domain.Common;

namespace WTO.IdbSubmissions.Persistence.Interceptors;

/// <summary>
/// EF Core interceptor that automatically populates audit fields on entities
/// </summary>
public class AuditInterceptor : SaveChangesInterceptor
{
    private readonly ICurrentUserService _currentUserService;

    /// <summary>
    /// Initializes a new instance of the AuditInterceptor class
    /// </summary>
    /// <param name="currentUserService">Current user service</param>
    public AuditInterceptor(ICurrentUserService currentUserService)
    {
        _currentUserService = currentUserService ?? throw new ArgumentNullException(nameof(currentUserService));
    }

    /// <summary>
    /// Intercepts SaveChanges operation to apply audit information
    /// </summary>
    /// <param name="eventData">Event data</param>
    /// <param name="result">Interception result</param>
    /// <returns>Interception result</returns>
    public override InterceptionResult<int> SavingChanges(DbContextEventData eventData, InterceptionResult<int> result)
    {
        ApplyAuditInformation(eventData.Context);
        return base.SavingChanges(eventData, result);
    }

    /// <summary>
    /// Intercepts SaveChangesAsync operation to apply audit information
    /// </summary>
    /// <param name="eventData">Event data</param>
    /// <param name="result">Interception result</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Interception result</returns>
    public override ValueTask<InterceptionResult<int>> SavingChangesAsync(
        DbContextEventData eventData, 
        InterceptionResult<int> result, 
        CancellationToken cancellationToken = default)
    {
        ApplyAuditInformation(eventData.Context);
        return base.SavingChangesAsync(eventData, result, cancellationToken);
    }

    /// <summary>
    /// Applies audit information to entities before saving
    /// </summary>
    /// <param name="context">Database context</param>
    private void ApplyAuditInformation(DbContext? context)
    {
        if (context == null) return;

        var currentUser = _currentUserService.GetCurrentUserId();
        var utcNow = DateTime.UtcNow;

        var entries = context.ChangeTracker.Entries<BaseEntity>()
            .Where(e => e.State == EntityState.Added || 
                       e.State == EntityState.Modified || 
                       e.State == EntityState.Deleted);

        foreach (var entry in entries)
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    // Set creation audit fields
                    SetPropertyValue(entry.Entity, nameof(BaseEntity.CreatedAt), utcNow);
                    SetPropertyValue(entry.Entity, nameof(BaseEntity.CreatedBy), currentUser);
                    break;

                case EntityState.Modified:
                    // Set modification audit fields
                    SetPropertyValue(entry.Entity, nameof(BaseEntity.ModifiedAt), utcNow);
                    SetPropertyValue(entry.Entity, nameof(BaseEntity.ModifiedBy), currentUser);
                    break;

                case EntityState.Deleted:
                    // For soft delete, set deletion audit fields and change state to Modified
                    if (entry.Entity.GetType().GetProperty(nameof(BaseEntity.IsDeleted)) != null)
                    {
                        entry.State = EntityState.Modified;
                        SetPropertyValue(entry.Entity, nameof(BaseEntity.IsDeleted), true);
                        SetPropertyValue(entry.Entity, nameof(BaseEntity.DeletedAt), utcNow);
                        SetPropertyValue(entry.Entity, nameof(BaseEntity.DeletedBy), currentUser);
                    }
                    break;
            }
        }
    }

    /// <summary>
    /// Sets a property value on an entity using reflection
    /// </summary>
    /// <param name="entity">Entity to update</param>
    /// <param name="propertyName">Property name</param>
    /// <param name="value">Value to set</param>
    private static void SetPropertyValue(object entity, string propertyName, object? value)
    {
        var property = entity.GetType().GetProperty(propertyName);
        if (property != null && property.CanWrite)
        {
            property.SetValue(entity, value);
        }
    }
}
