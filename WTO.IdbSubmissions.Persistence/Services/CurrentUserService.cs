using Microsoft.AspNetCore.Http;
using System.Security.Claims;
using WTO.IdbSubmissions.Application.Common.Interfaces;

namespace WTO.IdbSubmissions.Persistence.Services;

/// <summary>
/// Service for accessing the current authenticated user's information from HTTP context
/// </summary>
public class CurrentUserService : ICurrentUserService
{
    private readonly IHttpContextAccessor _httpContextAccessor;

    /// <summary>
    /// Initializes a new instance of the CurrentUserService class
    /// </summary>
    /// <param name="httpContextAccessor">HTTP context accessor</param>
    public CurrentUserService(IHttpContextAccessor httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
    }

    /// <summary>
    /// Gets the current user's identifier
    /// </summary>
    /// <returns>Current user identifier or null if not authenticated</returns>
    public string? GetCurrentUserId()
    {
        var user = _httpContextAccessor.HttpContext?.User;
        if (user?.Identity?.IsAuthenticated != true)
        {
            return null;
        }

        // Try to get the user identifier from different claim types
        // First try NameIdentifier, then Name, then preferred_username
        return user.FindFirst(ClaimTypes.NameIdentifier)?.Value ??
               user.FindFirst(ClaimTypes.Name)?.Value ??
               user.FindFirst("preferred_username")?.Value ??
               user.FindFirst("sub")?.Value;
    }

    /// <summary>
    /// Gets the current user's name
    /// </summary>
    /// <returns>Current user name or null if not authenticated</returns>
    public string? GetCurrentUserName()
    {
        var user = _httpContextAccessor.HttpContext?.User;
        if (user?.Identity?.IsAuthenticated != true)
        {
            return null;
        }

        // Try to get the user name from different claim types
        return user.FindFirst(ClaimTypes.Name)?.Value ??
               user.FindFirst("name")?.Value ??
               user.FindFirst("preferred_username")?.Value ??
               user.Identity.Name;
    }

    /// <summary>
    /// Indicates whether a user is currently authenticated
    /// </summary>
    /// <returns>True if authenticated, false otherwise</returns>
    public bool IsAuthenticated()
    {
        return _httpContextAccessor.HttpContext?.User?.Identity?.IsAuthenticated == true;
    }
}
